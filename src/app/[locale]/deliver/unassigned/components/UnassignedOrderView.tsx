"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import { Button } from "@udoy/components/ui/button";
import { Badge } from "@udoy/components/ui/badge";
import { Separator } from "@udoy/components/ui/separator";
import {
  MapPin,
  Phone,
  ArrowLeft,
  Info,
  CheckCircle,
  XCircle,
  Navigation,
  Calendar,
  Package,
} from "lucide-react";
import { OrderWithItems } from "../../types";
import Image from "next/image";
import { withError } from "@udoy/utils/app-error";
import { assignOrderToMe } from "../action";
import { toast } from "sonner";
import { UnitUtil } from "@udoy/utils/product-unit";
import Locale from "@udoy/components/Locale/Client";
import Hide from "@udoy/components/Hide";
import { useLocale } from "next-intl";

interface UnassignedOrderViewProps {
  order: OrderWithItems;
  isFirstOrder: boolean;
}

export function UnassignedOrderView({
  order,
  isFirstOrder,
}: UnassignedOrderViewProps) {
  const router = useRouter();
  const [isAssigning, setIsAssigning] = useState(false);
  const locale = useLocale();

  const handleAssignToMe = async () => {
    setIsAssigning(true);

    try {
      const result = await withError(assignOrderToMe(order.id));

      if (result) {
        router.push("/deliver");
        return toast.success("Order assigned To You");
      }
    } catch (error: any) {
      toast.error(error?.message || "Failed to assign order");
    }

    setIsAssigning(false);
  };

  const formatDate = (date: Date, locale: string) => {
    return new Date(date).toLocaleDateString(locale, {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatTime = (date: Date, locale: string) => {
    return new Date(date).toLocaleTimeString(locale, {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Group items by shop (if shop data is available)
  const itemsByShop = order.orderItems.reduce((acc, item) => {
    const shopName = item.product.shop?.name || "Unknown Shop";
    if (!acc[shopName]) {
      acc[shopName] = [];
    }
    acc[shopName].push(item);
    return acc;
  }, {} as Record<string, typeof order.orderItems>);

  return (
    <main className="container mx-auto px-4 py-6 max-w-md">
      <div className="flex flex-col min-h-[calc(100vh-2rem)]">
        {/* Header */}
        <div className="sticky top-0 z-10 bg-background pb-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => router.push("/deliver")}
                className="mr-2"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <h1 className="text-xl font-bold">
                Unassigned Order #{order.id}
              </h1>
            </div>
            <Badge
              variant="outline"
              className="text-xs bg-orange-100 text-orange-800 border-orange-200"
            >
              {order.status}
            </Badge>
          </div>
        </div>

        {/* Order Details */}
        <div className="space-y-4 mb-20">
          {/* Customer & Delivery Info Card */}
          <Card className="border-l-4 border-l-orange-400 overflow-hidden">
            <div className="p-4">
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h2 className="font-medium text-lg">{order.buyer.name}</h2>
                  <p className="text-sm text-muted-foreground">
                    Order #{order.id}
                  </p>
                </div>
                <div className="gap-1 flex">
                  <Badge className="bg-orange-800 text-white hover:bg-orange-700">
                    ৳{(order.subTotal + order.shipping).toLocaleString(locale)}
                  </Badge>
                  <Hide open={isFirstOrder}>
                    <Badge className="bg-red-900 text-white hover:bg-red-700">
                      <Locale en="New Customer" bn="নতুন গ্রাহক" />
                    </Badge>
                  </Hide>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-start">
                  <MapPin className="h-5 w-5 mr-2 mt-0.5 text-orange-500 flex-shrink-0 " />
                  <div>
                    <div className="font-medium">{order.address?.home}</div>
                    <div className="text-sm text-muted-foreground">
                      <Locale bn={order.address?.zone?.nam}>
                        {order.address?.zone?.name}
                      </Locale>
                    </div>
                  </div>
                </div>

                <div className="flex items-center">
                  <Phone className="h-5 w-5 mr-2 text-orange-500 flex-shrink-0" />
                  <Button variant="link" asChild className="h-auto p-0">
                    <a href={`tel:${order.address.phone}`}>
                      {order.address.phone}
                    </a>
                  </Button>
                </div>

                {/* Estimated distance - if available */}
                {/* 
                <div className="flex items-center">
                  <Navigation className="h-5 w-5 mr-2 text-orange-500 flex-shrink-0" />
                  <span className="font-medium">3.5 km away</span>
                </div> */}
              </div>
            </div>

            <Separator />

            <div className="p-4 grid grid-cols-2 gap-4">
              <div className="flex flex-col">
                <span className="text-sm text-muted-foreground">Payment</span>
                <span className="font-medium">Cash on Delivery</span>
              </div>
              <div className="flex flex-col">
                <span className="text-sm text-muted-foreground">
                  Order Date
                </span>
                <span className="font-medium">
                  {formatDate(order.createdAt, locale)} <br />
                  {formatTime(order.createdAt, locale)}
                </span>
              </div>
              {/* <div className="flex flex-col">
                <span className="text-sm text-muted-foreground">
                  Est. Pickup
                </span>
                <span className="font-medium">15 mins</span>
              </div>
              <div className="flex flex-col">
                <span className="text-sm text-muted-foreground">
                  Est. Delivery
                </span>
                <span className="font-medium">20 mins</span>
              </div> */}
            </div>

            {order.notes && (
              <>
                <Separator />
                <div className="p-4">
                  <div className="flex items-center mb-1">
                    <Info className="h-4 w-4 mr-1 text-orange-500" />
                    <span className="text-sm font-medium">
                      <Locale bn="বিশেষ নির্দেশনা">Special Instructions</Locale>
                    </span>
                  </div>
                  <div className="p-2 bg-orange-50 rounded-md text-sm">
                    {order.notes}
                  </div>
                </div>
              </>
            )}
          </Card>

          {/* Items List with Images */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">
                Order Items ({order.orderItems.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {Object.keys(itemsByShop).length > 0 ? (
                Object.entries(itemsByShop).map(([shopName, items], index) => (
                  <div key={shopName}>
                    {index > 0 && <Separator className="my-3" />}
                    <div className="mb-2">
                      <Badge variant="outline" className="text-xs">
                        {shopName}
                      </Badge>
                    </div>
                    <div className="space-y-3">
                      {items.map((item) => (
                        <div key={item.id} className="flex items-center">
                          <div className="h-16 w-16 relative rounded-md overflow-hidden bg-muted mr-3 flex-shrink-0">
                            <Image
                              src={
                                item.product.images[0]?.url ||
                                "/placeholder.svg"
                              }
                              alt={item.product.name}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <div className="flex-1">
                            <div className="font-medium">
                              <Locale bn={item.product.nam}>
                                {item.product.name}
                              </Locale>
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {UnitUtil.getAmountUnit(
                                item.product.amount * item.quantity,
                                item.product.unit,
                                locale
                              )}{" "}
                              x {item.quantity.toLocaleString(locale)}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-medium">
                              ৳
                              {(
                                (item.product.price - item.product.discount) *
                                item.quantity
                              ).toLocaleString(locale)}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              ৳
                              {(
                                item.product.price - item.product.discount
                              ).toLocaleString(locale)}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))
              ) : (
                <div className="space-y-3">
                  {order.orderItems.map((item) => (
                    <div key={item.id} className="flex items-center">
                      <div className="h-16 w-16 relative rounded-md overflow-hidden bg-muted mr-3 flex-shrink-0">
                        <Image
                          src={
                            item.product.images[0]?.url || "/placeholder.svg"
                          }
                          alt={item.product.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div className="flex-1">
                        <div className="font-medium">
                          <Locale bn={item.product.nam}>
                            {item.product.name}
                          </Locale>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {UnitUtil.getAmountUnit(
                            item.product.amount * item.quantity,
                            item.product.unit,
                            locale
                          )}{" "}
                          x {item.quantity.toLocaleString(locale)}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">
                          ৳{(item.price * item.quantity).toLocaleString(locale)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          ৳{item.price.toLocaleString(locale)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              <Separator className="my-3" />

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">
                    Subtotal
                  </span>
                  <span className="text-sm">
                    ৳{order.subTotal.toLocaleString(locale)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">
                    Shipping
                  </span>
                  <span className="text-sm">
                    ৳{order.shipping.toLocaleString(locale)}
                  </span>
                </div>
                <div className="flex justify-between font-medium text-lg">
                  <span>Total:</span>
                  <span>
                    ৳{(order.subTotal + order.shipping).toLocaleString(locale)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Footer Action Bar */}
        <div className="fixed bottom-0 left-0 right-0 bg-background border-t p-4">
          <div className=" flex gap-3">
            <Button
              variant="outline"
              onClick={() => router.push("/deliver")}
              className="flex-1"
              size="lg"
              disabled={isAssigning}
            >
              <XCircle className="mr-2 h-5 w-5" />
              Decline
            </Button>
            <Button
              onClick={handleAssignToMe}
              className="flex-1 bg-orange-500 hover:bg-orange-600 dark:text-white"
              size="lg"
              disabled={isAssigning}
            >
              {isAssigning ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              ) : (
                <CheckCircle className="mr-2 h-5 w-5" />
              )}
              {isAssigning ? "Assigning..." : "Accept Order"}
            </Button>
          </div>
        </div>
      </div>
    </main>
  );
}
